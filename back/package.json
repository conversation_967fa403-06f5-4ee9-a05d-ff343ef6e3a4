{"name": "restaurant-backend", "version": "1.0.0", "description": "Restaurant management backend with admin and user systems", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "sequelize": "^6.35.2", "winston": "^3.11.0"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}}