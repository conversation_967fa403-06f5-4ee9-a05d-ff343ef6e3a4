const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { Op } = require("sequelize");
const User = require("../models/User");
const Order = require("../models/Order");
const ResponseHandler = require("../utils/responseHandler");
const logger = require("../utils/logger");

// User Authentication
const registerUser = async (req, res) => {
  try {
    const { name, email, password, phone, address } = req.body;

    logger.info("User registration attempt:", { name, email });

    // Validate input
    if (!name || !email || !password) {
      return ResponseHandler.error(
        res,
        "Name, email, and password are required",
        400
      );
    }

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return ResponseHandler.conflict(
        res,
        "User with this email already exists"
      );
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const user = await User.create({
      name,
      email,
      password: hashedPassword,
      phone,
      address,
    });

    // Generate JWT token
    if (!process.env.JWT_SECRET) {
      throw new Error("JWT_SECRET is not configured");
    }

    const token = jwt.sign(
      { id: user.id, email: user.email, role: "user" },
      process.env.JWT_SECRET,
      { expiresIn: "24h" }
    );
    console.log(token);
    ResponseHandler.success(
      res,
      {
        token,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          address: user.address,
        },
      },
      "User registered successfully",
      201
    );
  } catch (error) {
    logger.error("User registration error:", error);
    ResponseHandler.error(res, "Server error during registration");
  }
};

const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;

    logger.info("User login attempt:", { email });

    // Validate input
    if (!email || !password) {
      return ResponseHandler.error(res, "Email and password are required", 400);
    }

    // Find user
    const user = await User.findOne({ where: { email } });
    if (!user) {
      return ResponseHandler.unauthorized(res, "Invalid credentials");
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return ResponseHandler.unauthorized(res, "Invalid credentials");
    }

    // Generate JWT token
    if (!process.env.JWT_SECRET) {
      throw new Error("JWT_SECRET is not configured");
    }

    const token = jwt.sign(
      { id: user.id, email: user.email, role: "user" },
      process.env.JWT_SECRET,
      { expiresIn: "24h" }
    );

    ResponseHandler.success(
      res,
      {
        token,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          address: user.address,
        },
      },
      "Login successful"
    );
  } catch (error) {
    logger.error("User login error:", error);
    ResponseHandler.error(res, "Server error during login");
  }
};

// User Profile Management
const getUserProfile = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ["password"] },
    });
    ResponseHandler.success(res, user, "User profile retrieved successfully");
  } catch (error) {
    logger.error("Get user profile error:", error);
    ResponseHandler.error(res, "Error fetching user profile");
  }
};

const updateUserProfile = async (req, res) => {
  try {
    const { name, phone, address } = req.body;
    const [updated] = await User.update(
      { name, phone, address },
      { where: { id: req.user.id } }
    );

    if (updated) {
      const updatedUser = await User.findByPk(req.user.id, {
        attributes: { exclude: ["password"] },
      });
      ResponseHandler.success(res, updatedUser, "Profile updated successfully");
    } else {
      ResponseHandler.notFound(res, "User");
    }
  } catch (error) {
    logger.error("Update user profile error:", error);
    ResponseHandler.error(res, "Error updating profile");
  }
};

// Order Management
const createOrder = async (req, res) => {
  try {
    const {
      items,
      totalAmount,
      deliveryAddress,
      customerName,
      customerPhone,
      tableNumber,
      orderNotes,
      orderType,
    } = req.body;

    const order = await Order.create({
      userId: req.user.id,
      items,
      totalAmount,
      deliveryAddress,
      customerName,
      customerPhone,
      tableNumber,
      orderNotes,
      orderType: orderType || "dine-in",
    });

    ResponseHandler.success(res, order, "Order created successfully", 201);
  } catch (error) {
    logger.error("Create order error:", error);
    ResponseHandler.error(res, "Error creating order");
  }
};

const getUserOrders = async (req, res) => {
  try {
    const orders = await Order.findAll({
      where: { userId: req.user.id },
      order: [["createdAt", "DESC"]],
    });
    ResponseHandler.success(res, orders, "Orders retrieved successfully");
  } catch (error) {
    logger.error("Get user orders error:", error);
    ResponseHandler.error(res, "Error fetching orders");
  }
};

const cancelOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    logger.info("Order cancellation attempt:", { orderId: id, userId });

    const order = await Order.findOne({
      where: { id, userId },
    });

    if (!order) {
      return ResponseHandler.notFound(res, "Order");
    }

    if (order.status === "cancelled") {
      return ResponseHandler.error(res, "Order is already cancelled", 400);
    }

    if (order.status !== "pending") {
      return ResponseHandler.error(
        res,
        "Only pending orders can be cancelled",
        400
      );
    }

    order.status = "cancelled";
    await order.save();

    logger.info("Order cancelled successfully:", { orderId: id, userId });

    ResponseHandler.success(res, "Order cancelled successfully", {
      order: {
        id: order.id,
        status: order.status,
      },
    });
  } catch (error) {
    logger.error("Error cancelling order:", error);
    ResponseHandler.error(res, "Failed to cancel order");
  }
};

// Get User Notification Count
const getUserNotificationCount = async (req, res) => {
  try {
    const userId = req.user.id;

    logger.info("User notification count request:", { userId });

    if (!userId) {
      logger.error("No user ID found in request user");
      return ResponseHandler.error(res, "Authentication error", 401);
    }

    // Count user's active orders as notifications
    const activeOrdersCount = await Order.count({
      where: {
        userId,
        status: {
          [Op.in]: ["pending", "preparing", "ready"],
        },
      },
    });

    // Count recent orders (last 24 hours) as additional notifications
    const recentOrdersCount = await Order.count({
      where: {
        userId,
        createdAt: {
          [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
    });

    const totalNotifications = activeOrdersCount + Math.min(recentOrdersCount, 3); // Cap recent notifications

    logger.info("User notification count retrieved successfully:", { 
      userId, 
      activeOrders: activeOrdersCount,
      recentOrders: recentOrdersCount,
      totalNotifications 
    });

    ResponseHandler.success(res, "Notification count retrieved successfully", {
      count: totalNotifications,
      activeOrders: activeOrdersCount,
      recentOrders: recentOrdersCount,
    });
  } catch (error) {
    logger.error("Error getting user notification count:", error);
    ResponseHandler.error(res, "Failed to get notification count");
  }
};

// Get User Notifications (Detailed)
const getUserNotifications = async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 10, offset = 0 } = req.query;

    logger.info("User notifications request:", { userId, limit, offset });

    // Get active orders
    const activeOrders = await Order.findAll({
      where: {
        userId,
        status: {
          [Op.in]: ["pending", "preparing", "ready"],
        },
      },
      order: [["createdAt", "DESC"]],
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    // Get recent orders
    const recentOrders = await Order.findAll({
      where: {
        userId,
        status: {
          [Op.notIn]: ["pending", "preparing", "ready"], // Not active
        },
        createdAt: {
          [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
      order: [["createdAt", "DESC"]],
      limit: Math.min(parseInt(limit), 3), // Limit recent notifications
      offset: parseInt(offset),
    });

    const notifications = [
      ...activeOrders.map(order => ({
        id: `active-${order.id}`,
        type: "active_order",
        title: `Order #${order.id} Status`,
        message: `Your order is ${order.status}`,
        data: order,
        createdAt: order.createdAt,
        priority: "high",
      })),
      ...recentOrders.map(order => ({
        id: `recent-${order.id}`,
        type: "recent_order",
        title: "Recent Order",
        message: `Order #${order.id} - ${order.status}`,
        data: order,
        createdAt: order.createdAt,
        priority: "medium",
      })),
    ].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    logger.info("User notifications retrieved successfully:", { 
      userId, 
      totalNotifications: notifications.length 
    });

    ResponseHandler.success(res, "Notifications retrieved successfully", {
      notifications,
      total: notifications.length,
    });
  } catch (error) {
    logger.error("Error getting user notifications:", error);
    ResponseHandler.error(res, "Failed to get notifications");
  }
};

module.exports = {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  createOrder,
  getUserOrders,
  cancelOrder,
  getUserNotificationCount,
  getUserNotifications,
};
