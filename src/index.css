@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Hotel Menu Design System - All colors MUST be HSL */

@layer base {
  :root {
    /* Clean Hotel Menu Core Colors */
    --background: 0 0% 100%;
    --foreground: 222 20% 15%;

    /* Hotel Card & Surface Colors */
    --card: 0 0% 98%;
    --card-foreground: 222 20% 15%;

    /* Popover & Overlay */
    --popover: 0 0% 100%;
    --popover-foreground: 222 20% 15%;

    /* Professional Primary - Clean Blue */
    --primary: 220 91% 47%;
    --primary-foreground: 0 0% 100%;

    /* Hotel Secondary Colors */
    --secondary: 210 12% 95%;
    --secondary-foreground: 222 20% 20%;

    /* Clean Muted Colors */
    --muted: 210 12% 96%;
    --muted-foreground: 222 20% 45%;

    /* Professional Accent - Subtle Teal */
    --accent: 190 70% 45%;
    --accent-foreground: 0 0% 100%;

    /* Hotel Brand Colors */
    --hotel: 210 12% 95%;
    --hotel-foreground: 222 20% 20%;

    /* Professional Gold/Amber Accents */
    --gold: 38 75% 55%;
    --gold-foreground: 0 0% 100%;

    /* Clean Pearl/Cream */
    --pearl: 45 15% 96%;
    --pearl-foreground: 222 20% 20%;

    /* Luxury Dark Colors */
    --luxury: 222 20% 15%;
    --luxury-foreground: 0 0% 100%;

    /* Professional Destructive */
    --destructive: 0 70% 55%;
    --destructive-foreground: 0 0% 100%;

    /* Clean Form Elements */
    --border: 210 12% 88%;
    --input: 0 0% 100%;
    --ring: 220 91% 47%;

    /* Clean Hotel Gradients */
    --hotel-gradient: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(210 12% 98%) 50%, hsl(210 12% 95%) 100%);
    --gold-gradient: linear-gradient(135deg, hsl(38 75% 50%) 0%, hsl(38 75% 55%) 50%, hsl(38 75% 60%) 100%);
    --pearl-gradient: linear-gradient(135deg, hsl(45 15% 94%) 0%, hsl(45 15% 96%) 50%, hsl(45 15% 98%) 100%);
    --luxury-gradient: linear-gradient(135deg, hsl(222 20% 20%) 0%, hsl(222 20% 15%) 50%, hsl(222 20% 10%) 100%);
    --premium-glow: radial-gradient(circle at center, hsl(220 91% 47% / 0.05) 0%, transparent 70%);
    --professional-glow: radial-gradient(circle at center, hsl(220 91% 47% / 0.03) 0%, transparent 70%);

    /* Clean Professional Shadows */
    --shadow-hotel: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-gold: 0 4px 6px -1px hsl(38 75% 50% / 0.1), 0 2px 4px -2px hsl(38 75% 50% / 0.1);
    --shadow-pearl: 0 2px 4px -1px hsl(45 15% 85% / 0.1), 0 1px 2px -1px hsl(45 15% 80% / 0.06);
    --shadow-professional: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -4px hsl(0 0% 0% / 0.1);

    /* Border Radius */
    --radius: 0.75rem;

    /* Clean Hotel Sidebar */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 222 20% 20%;
    --sidebar-primary: 220 91% 47%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 12% 95%;
    --sidebar-accent-foreground: 222 20% 20%;
    --sidebar-border: 210 12% 88%;
    --sidebar-ring: 220 91% 47%;
  }

  .dark {
    /* Professional dark mode for hotel menu */
    --background: 222 20% 8%;
    --foreground: 210 12% 95%;
    --card: 222 20% 10%;
    --card-foreground: 210 12% 92%;
    --popover: 222 20% 12%;
    --popover-foreground: 210 12% 90%;
    --secondary: 222 20% 15%;
    --secondary-foreground: 210 12% 85%;
    --muted: 222 20% 12%;
    --muted-foreground: 210 12% 65%;
    --accent: 190 70% 45%;
    --accent-foreground: 0 0% 100%;
    --border: 222 20% 20%;
    --input: 222 20% 15%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    background-image: var(--professional-glow);
  }

  /* Professional Hotel Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-playfair;
  }

  /* Clean scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-primary/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/50;
  }
}

@layer components {
  /* Professional Hotel Button Styles */
  .btn-hotel {
    @apply bg-primary text-primary-foreground border border-primary shadow-hotel hover:shadow-professional transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border border-border shadow-hotel hover:shadow-professional transition-all duration-200;
  }

  /* Clean Hotel Card Effects */
  .card-hotel {
    @apply bg-card border border-border shadow-hotel;
  }

  .card-hover {
    @apply hover:shadow-professional hover:border-primary/20 hover:-translate-y-0.5 transition-all duration-200;
  }

  /* Professional text highlight */
  .text-highlight {
    @apply text-primary font-medium;
  }

  /* Clean Hotel Divider */
  .divider-hotel {
    @apply border-t border-border relative;
  }
}