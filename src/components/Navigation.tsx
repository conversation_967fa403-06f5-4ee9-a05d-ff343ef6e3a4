import React, { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Utensils,
  User,
  LogOut,
  Settings,
  ShoppingCart,
  Home,
  ChefHat,
  Bell,
} from "lucide-react";
import { useAuth } from "@/context/authContext";

interface UserProfile {
  name: string;
  email: string;
  phone?: string;
  address?: string;
}

interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  priority: string;
  createdAt: string;
}

export const Navigation = () => {
  const { userToken, adminToken, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [notificationCount, setNotificationCount] = useState(0);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!userToken && !adminToken) {
        setUserProfile(null);
        return;
      }

      setIsLoadingProfile(true);
      try {
        const token = userToken || adminToken;
        const endpoint = userToken ? "/api/user/profile" : "/api/admin/profile";
        
        const response = await fetch(`http://localhost:5000${endpoint}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setUserProfile({
            name: data.data.name || data.data.username || "User",
            email: data.data.email || "<EMAIL>",
            phone: data.data.phone,
            address: data.data.address,
          });
        }
      } catch (error) {
        console.error("Failed to fetch user profile:", error);
        // Set default profile data
        setUserProfile({
          name: userToken ? "Customer" : "Administrator",
          email: userToken ? "<EMAIL>" : "<EMAIL>",
        });
      } finally {
        setIsLoadingProfile(false);
      }
    };

    fetchUserProfile();
  }, [userToken, adminToken]);

  // Fetch notification count
  useEffect(() => {
    const fetchNotifications = async () => {
      if (!userToken && !adminToken) {
        setNotificationCount(0);
        return;
      }

      try {
        const token = userToken || adminToken;
        const endpoint = userToken ? "/api/user/notifications/count" : "/api/admin/notifications/count";
        
        const response = await fetch(`http://localhost:5000${endpoint}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setNotificationCount(data.data.count || 0);
        }
      } catch (error) {
        console.error("Failed to fetch notifications:", error);
        setNotificationCount(0);
      }
    };

    // Fetch detailed notifications
    const fetchDetailedNotifications = async () => {
      if (!userToken && !adminToken) {
        setNotifications([]);
        return;
      }

      setIsLoadingNotifications(true);
      try {
        const token = userToken || adminToken;
        const endpoint = userToken ? "/api/user/notifications" : "/api/admin/notifications";
        
        const response = await fetch(`http://localhost:5000${endpoint}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setNotifications(data.data.notifications || []);
        }
      } catch (error) {
        console.error("Failed to fetch detailed notifications:", error);
        setNotifications([]);
      } finally {
        setIsLoadingNotifications(false);
      }
    };

    // Handle notification click
    const handleNotificationClick = () => {
      setIsNotificationsOpen(!isNotificationsOpen);
      if (!isNotificationsOpen) {
        fetchDetailedNotifications();
      }
    };

    fetchNotifications();
}, [userToken, adminToken]);

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <Link to="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-lg">
                <Utensils className="h-6 w-6 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                Epicurean
              </span>
            </Link>

            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-1">
              <Link
                to="/"
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive("/")
                    ? "bg-orange-100 text-orange-700"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                <Home className="h-4 w-4 inline mr-1" />
                Menu
              </Link>

              {userToken && (
                <Link
                  to="/user/dashboard"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive("/user/dashboard")
                      ? "bg-orange-100 text-orange-700"
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <User className="h-4 w-4 inline mr-1" />
                  My Orders
                </Link>
              )}

              {adminToken && (
                <Link
                  to="/admin/dashboard"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive("/admin/dashboard")
                      ? "bg-orange-100 text-orange-700"
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <ChefHat className="h-4 w-4 inline mr-1" />
                  Admin Panel
                </Link>
              )}
            </div>
          </div>

          {/* Right side - Auth and Actions */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            {(userToken || adminToken) && (
              <DropdownMenu open={isNotificationsOpen} onOpenChange={setIsNotificationsOpen}>
                <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-5 w-5" />
                    {notificationCount > 0 && (
                <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs">
                        {notificationCount}
                </Badge>
                    )}
              </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-80" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Notifications</span>
                      {isLoadingNotifications && (
                        <span className="text-xs text-muted-foreground">Loading...</span>
                      )}
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {notifications.length === 0 ? (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      {isLoadingNotifications ? "Loading notifications..." : "No notifications"}
                    </div>
                  ) : (
                    notifications.map((notification) => (
                      <DropdownMenuItem key={notification.id} className="cursor-pointer">
                        <div className="flex flex-col space-y-1 w-full">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{notification.title}</span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              notification.priority === "high" 
                                ? "bg-red-100 text-red-700" 
                                : "bg-blue-100 text-blue-700"
                            }`}>
                              {notification.priority}
                            </span>
                          </div>
                          <span className="text-xs text-muted-foreground">{notification.message}</span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(notification.createdAt).toLocaleString()}
                          </span>
                        </div>
                      </DropdownMenuItem>
                    ))
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* User Menu */}
            {userToken ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-8 w-8 rounded-full"
                  >
                    <div className="h-8 w-8 rounded-full bg-gradient-to-r from-orange-400 to-red-400 flex items-center justify-center">
                      <User className="h-4 w-4 text-white" />
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {isLoadingProfile ? "Loading..." : userProfile?.name || "Customer"}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {isLoadingProfile ? "..." : userProfile?.email || "<EMAIL>"}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/user/dashboard" className="cursor-pointer">
                      <User className="mr-2 h-4 w-4" />
                      <span>My Orders</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/user/profile" className="cursor-pointer">
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Profile Settings</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="cursor-pointer"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : adminToken ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-8 w-8 rounded-full"
                  >
                    <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center">
                      <ChefHat className="h-4 w-4 text-white" />
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {isLoadingProfile ? "Loading..." : userProfile?.name || "Administrator"}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {isLoadingProfile ? "..." : userProfile?.email || "<EMAIL>"}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/admin/dashboard" className="cursor-pointer">
                      <ChefHat className="mr-2 h-4 w-4" />
                      <span>Admin Dashboard</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/admin/menu" className="cursor-pointer">
                      <Utensils className="mr-2 h-4 w-4" />
                      <span>Manage Menu</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="cursor-pointer"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Button asChild variant="ghost" size="sm">
                  <Link to="/login">Sign In</Link>
                </Button>
                <Button
                  asChild
                  size="sm"
                  className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                >
                  <Link to="/signup">Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
